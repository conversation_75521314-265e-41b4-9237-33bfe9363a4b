
#ifndef sps30_h
#define sps30_h

#include "Arduino.h" 

typedef int64_t s64;
typedef uint64_t u64;
typedef int32_t s32;
typedef uint32_t u32;
typedef int16_t s16;
typedef unsigned short u16;   
typedef int8_t s8;
typedef uint8_t u8;

/* Types not typically provided by <stdint.h> */
typedef float f32;

#define ERR_OK          0x00
#define ERR_DATALENGTH  0X01
#define ERR_UNKNOWNCMD  0x02
#define ERR_ACCESSRIGHT 0x03
#define ERR_PARAMETER   0x04
#define ERR_OUTOFRANGE  0x28
#define ERR_CMDSTATE    0x43
#define ERR_TIMEOUT     0x50
#define ERR_PROTOCOL    0x51
#define ERR_TEST        0x99

#define v_MassPM1 1
#define v_MassPM2 2
#define v_MassPM4 3
#define v_MassPM10 4
#define v_NumPM0 5
#define v_NumPM1 6
#define v_NumPM2 7
#define v_NumPM4 8
#define v_NumPM10 9
#define v_PartSize 10

typedef enum Serial_port {
  I2C_COMMS = 0,
  SOFTWARE_SERIAL = 1,
  SERIALPORT = 2,
  SERIALPORT1 = 3,
  SERIALPORT2 = 4,
  SERIALPORT3 = 5,
  NONE = 6
};

typedef struct sps_values
{
    f32   MassPM1;        // Mass Concentration PM1.0 [μg/m3]
    f32   MassPM2;        // Mass Concentration PM2.5 [μg/m3]
    f32   MassPM4;        // Mass Concentration PM4.0 [μg/m3]
    f32   MassPM10;       // Mass Concentration PM10 [μg/m3]
    f32   NumPM0;         // Number Concentration PM0.5 [#/cm3]
    f32   NumPM1;         // Number Concentration PM1.0 [#/cm3]
    f32   NumPM2;         // Number Concentration PM2.5 [#/cm3]
    f32   NumPM4;         // Number Concentration PM4.0 [#/cm3]
    f32   NumPM10;        // Number Concentration PM4.0 [#/cm3]
    f32   PartSize;       // Typical Particle Size [μm]
};

typedef union {
    u8 array[4];
	  u32 u;
    f32 value;
} ByteToFloat;

#define SER_START_MEASUREMENT       0x00
#define SER_STOP_MEASUREMENT        0x01
#define SER_READ_MEASURED_VALUE     0x03
#define SER_START_FAN_CLEANING      0x56
#define SER_RESET                   0xD3

#define SER_READ_DEVICE_INFO            0xD0    // GENERIC device request
#define SER_READ_DEVICE_PRODUCT_NAME    0xF1
#define SER_READ_DEVICE_ARTICLE_CODE    0xF2
#define SER_READ_DEVICE_SERIAL_NUMBER   0xF3

#define SER_AUTO_CLEANING_INTERVAL  0x80    // Generic autoclean request
#define SER_READ_AUTO_CLEANING          0x81    // read autoclean
#define SER_WRITE_AUTO_CLEANING         0x82    // write autoclean

#define SHDLC_IND   0x7e                   // header & trailer
#define TIME_OUT    5000                  // timeout to prevent deadlock read
#define RX_DELAY_MS 200                    // wait between write and read

#define Max_buffer_rc_len 50

class SPS30
{
  public:
    SPS30(void);
    void begin(Serial_port port, u32 speed);
    u8 getAutoClean(u32* val);
    u8 setAutoclean(u32 val);
    u8 Getvalue(struct sps_values *v);
    uint8_t GetSerialNumber(char *ser, uint8_t len) {return(Get_Device_info( SER_READ_DEVICE_SERIAL_NUMBER, ser, len));}
    uint8_t GetArticleCode(char *ser, uint8_t len)  {return(Get_Device_info( SER_READ_DEVICE_ARTICLE_CODE, ser, len));}
    uint8_t GetProductName(char *ser, uint8_t len)  {return(Get_Device_info(SER_READ_DEVICE_PRODUCT_NAME, ser, len));}
    bool reset() {return(Instruct(SER_RESET));}
    bool start() {return(Instruct(SER_START_MEASUREMENT));}
    bool stop() {return(Instruct(SER_STOP_MEASUREMENT));}
    bool clean() {return(Instruct(SER_START_FAN_CLEANING));}
    u8 Reset(void);
	private:
    u8 _Receive_buf[Max_buffer_rc_len];
    u8 _Send_buf[10];
    u8 _Receive_buf_len;
    u8 _Send_buf_len;
    bool _started;
    u8 Get_Device_info(u8 type, char* ser, u8 len);
    f32 byte_to_float(u8 x);
    bool Fill_command(u8 command,u32 parameter = 0);
    u8 Calc_CRC(u8* buf, u8 first, u8 last);
    u8 SerialToBuffer();
    u8 SendToSerial();
    u8 ReadFromSerial();
    u8 ByteUnStuff(u8 b);
    int ByteStuff(u8 b, int off);
    bool Instruct(u8 type);
    uint8_t Serial_RX = 0, Serial_TX = 0;
    Stream *_serial;
};
#endif
