#include "sps30.h"
#include "SoftwareSerial.h"
SPS30::SPS30(){
  _Receive_buf_len = 0;
  _Send_buf_len = 0;
}
void SPS30::begin(Serial_port port,u32 speed){
  switch(port){
    case SERIALPORT:
      Serial.begin(speed);
      _serial = &Serial;
      break;
#if defined(__AVR_ATmega1280__) || defined(__AVR_ATmega2560)
    case SERIALPORT1:
      Serial1.begin(speed);
      _serial = &Serial1;
      break;
    case SERIALPORT2:
      Serial2.begin(speed);
      _serial = &Serial2;
      break;
    case SERIALPORT3:
      Serial3.begin(speed);
      _serial = &Serial3;
      break;
#endif
#if defined(ARDUINO_ARCH_ESP32)
    case SERIALPORT1:
      if (Serial_RX == 0 || Serial_TX == 0){
        return false;
      }
      Serial1.begin(speed, SERIAL_8N1, Serial_RX, Serial_TX, false);
      _serial = &Serial1;
      break;
    case SERIALPORT2:
      Serial2.begin(speed);
      _serial = &Serial2;
      break;
#endif
    default:
      if (Serial_RX == 0 || Serial_TX == 0){
        return false;
      }
      if (Serial_RX == 8 && Serial_TX == 8) {
        Serial.begin(speed);
        _serial = &Serial;
      }
      else{}
  }
}
bool SPS30::Instruct(u8 type){
  u8 ret;
  if(type == SER_START_FAN_CLEANING){
    if(_started == false) return(false);
  }
  if(Fill_command(type) != true) return(ERR_PARAMETER);
  ret = ReadFromSerial();
  if (ret == ERR_OK){
    if (type == SER_START_MEASUREMENT) {
      _started = true;
      delay(1000);
    }
    else if (type == SER_STOP_MEASUREMENT)
      _started = false;
    else if (type == SER_RESET){
      _started = false;
      delay(2000);
    }
    return(true);
  }
  return(false);
}

u8 SPS30::Get_Device_info(u8 type, char* ser, u8 len){
  u8 ret, i, offset;
  if(Fill_command(type) != true) return(ERR_PARAMETER);
  ret = ReadFromSerial();
  offset = 5;
  if(ret != ERR_OK) return(ret);
  for(i=0;i<len;i++){
    ser[i] = _Receive_buf[i+offset];
    if(ser[i] == 0x0) break;
  }
  return(ERR_OK);
}

bool SPS30::Fill_command(u8 command, u32 parameter){
  memset(_Send_buf,0x0,sizeof(_Send_buf));
  _Send_buf_len = 0;
  u16 i = 0;
  u8 tmp;
  _Send_buf[i++] = SHDLC_IND;
  _Send_buf[i++] = 0x00;
  _Send_buf[i++] = command;
  switch(command){
    case SER_START_MEASUREMENT:
      _Send_buf[i++] = 2;
      _Send_buf[i++] = 0x1;
      _Send_buf[i++] = 0x3;
      break;
    case SER_STOP_MEASUREMENT:
    case SER_READ_MEASURED_VALUE:
    case SER_START_FAN_CLEANING:
    case SER_RESET:
      _Send_buf[i++] = 0;     // length
      break;
    case SER_READ_DEVICE_PRODUCT_NAME:
    case SER_READ_DEVICE_ARTICLE_CODE:
    case SER_READ_DEVICE_SERIAL_NUMBER:
      _Send_buf[2] = SER_READ_DEVICE_INFO;
      _Send_buf[i++] = 1;     // length
      _Send_buf[i++] = command & 0x0f;
      break;
    case SER_READ_AUTO_CLEANING:
      _Send_buf[2] = SER_AUTO_CLEANING_INTERVAL;
      _Send_buf[i++] = 1;     // length
      _Send_buf[i++] = 0;     // Subcommand, this value must be set to 0x00
      break;
    default:
      return(false);
      break;
  }
  tmp = Calc_CRC(_Send_buf,1,i);
  i = ByteStuff(tmp,i);
  _Send_buf[i] = SHDLC_IND;
  _Send_buf_len = ++i;
  
  return true;
}

uint8_t SPS30::Calc_CRC(uint8_t *buf, uint8_t first, uint8_t last)
{
  uint8_t i;
  uint32_t ret = 0;
  for (i = first; i <= last ; i ++)   ret += buf[i];
  return(~(ret & 0xff));
}

u8 SPS30::SendToSerial(){
  u8 i;
  if(_Send_buf_len == 0) return(ERR_DATALENGTH);
  _serial->write(_Send_buf,_Send_buf_len);
  _Send_buf_len = 0;
  return(ERR_OK);
}
u8 SPS30::ReadFromSerial(){
  u8 ret;
  _serial->flush();
  ret = SendToSerial();
  if(ret != ERR_OK) return(ret);
  delay(RX_DELAY_MS);
  ret = SerialToBuffer(); 
  if(ret != ERR_OK) return(ret);
  ret = Calc_CRC(_Receive_buf,1,_Receive_buf_len-2);
  if(_Receive_buf[_Receive_buf_len-1] != ret) return(ERR_PROTOCOL);
  else;
  return (_Receive_buf[3]);
}
u8 SPS30::SerialToBuffer(){
  u32 startTime;
  bool byte_stuff = false;
  u8 i,k,j;
  byte response[50];
  startTime = millis();
  i=0,j=0,k=0;
  while (true){
   if((millis()-startTime) > 5000) break;
    if(_serial->available()){
      while(_serial->available()){
        response[k++] = _serial->read();
        delay(3);
        if(k > 50) break;
      }
      break;
    }
  }
  while(true){
    _Receive_buf[i] = response[j];
    if (i == 0) {
      if (_Receive_buf[i] != SHDLC_IND){
        return(ERR_PROTOCOL);
      }
    }
    else{
      if (_Receive_buf[i] == 0x7D) {
        i--;              
        byte_stuff = true;
      }
      else if (byte_stuff) {
        _Receive_buf[i] = ByteUnStuff(_Receive_buf[i]);
        byte_stuff = false;
      }
      else if (_Receive_buf[i] == SHDLC_IND) {
        _Receive_buf_len = i;
        if (_Receive_buf_len < 3) return(ERR_PROTOCOL);
          return(ERR_OK);
        }
      }
      i++;
      j++;
      if(j > k)
      {
        return(ERR_PROTOCOL);
      }
   }
   memset(response,0,sizeof(response));
}
u8 SPS30::Getvalue(struct sps_values *v){
  u8 ret, loop;
  u8 offset;
  offset = 5;
  if(Fill_command(SER_READ_MEASURED_VALUE) != true) return(ERR_PARAMETER);
  ret = ReadFromSerial();
  if (ret != ERR_OK) return (ret);
  if(_Receive_buf[4] != 0x28) return (ERR_DATALENGTH);
  else;
  memset(v,0x0,sizeof(struct sps_values));
  v->MassPM1 = byte_to_float(offset);
  v->MassPM2 = byte_to_float(offset + 4);
  v->MassPM4 = byte_to_float(offset + 8);
  v->MassPM10 = byte_to_float(offset + 12);
  return(ERR_OK);
}
f32 SPS30::byte_to_float(u8 x){
  ByteToFloat conv;
  for(byte i=0;i<4;i++){
    conv.array[3-i] = _Receive_buf[x+i];
  }
  return conv.value;
}

u8 SPS30::ByteUnStuff(u8 b){
  switch(b){
    case 0x31: return(0x11);
    case 0x33: return(0x13);
    case 0x5d: return(0x7d);
    case 0x5e: return(0x7e);
    default:
      return(0);
  }
}
int SPS30::ByteStuff(u8 b, int off)
{
    u8  x = 0;
    switch(b){
        case 0x11: {x = 0x31; break;}
        case 0x13: {x = 0x33; break;}
        case 0x7d: {x = 0x5d; break;}
        case 0x7e: {x = 0x5e; break;}
    }
    if (x == 0) _Send_buf[off++] = b;
    else
    {
        _Send_buf[off++] = 0x7D;
        _Send_buf[off++] = x;
    }
    return(off);
}
