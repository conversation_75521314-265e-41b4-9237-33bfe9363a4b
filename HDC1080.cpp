#include "HDC1080.h"
ClosedCube_HDC1080 hdc1080;
HDC1080::HDC1080()
{
}

HDC1080::~HDC1080()
{
}

void HDC1080::setup(){
  hdc1080.begin(0x40);
	read_calib();
  
}

void HDC1080::calibrate(float a,float b){

  boolean _b = (a != 0) && (b != 0);
        if (b == false) {
            //Serial.println("Ban tin khong hop le");
            return;
        }
        else {
            write_value(a, 120);
            write_value(b, 125);
        }
}

void HDC1080::read_calib(){
  oTemp = read_value(120);
  oHum = read_value(125);
  if(isnan(oTemp) || oTemp > 65 || oTemp < -65) {
    oTemp = 0.0;
    write_value(oTemp, 120);
  }
  if(isnan(oHum) || oHum > 120 || oHum < -120) {
    oHum = 0.0;
    write_value(oHum, 125);
  }
}

void HDC1080::read() {
  float tempC=hdc1080.readTemperature() + oTemp;
  float tempF=tempC*1.8+32;
  float hum=hdc1080.readHumidity() + oHum;
	if (tempC < -10 || tempC > 65) {
    value.temp = -1;
	}else{
    value.temp = tempC;
  }
  if(tempC != -1){
    if((hum < 0 || hum > 99.9899)){
      value.humi = -1;
    }else{
      value.humi = hum;
    }
  }else{
    if(hum > 99.9899 || hum < 0){
      value.humi = -1;
    }else{
      value.humi = hum;
    }
  }
  return value;
}

int HDC1080::check_status_sensor(){
  boolean status_hdc = false;
    for (int i = 0; i < 20; i++) {
        if (count_err_hdc[i] != 1){
          status_hdc = true;
          break;
   		}
   	}
    return status_hdc;
}
float HDC1080::read_value(int _add) {
//	 byte f_b, s_b, t_b, fo_b;
//  f_b = EEPROM.read(_add);
//  delay(10);
//  if (f_b == 0xFF) {
//    f_b = 0;
//  }
//  s_b = EEPROM.read(_add+1);
//  delay(10);
//  if (s_b == 0xFF) {
//    s_b = 0;
//  }
//  t_b = EEPROM.read(_add+2);
//  delay(10);
//  if (t_b == 0xFF) {
//    t_b = 0;
//  }
//  fo_b = EEPROM.read(_add+3);
//  delay(10);
//  if (fo_b == 0xFF) {
//    fo_b = 0;
//  }
//  return byte_to_float(fo_b, t_b, s_b, f_b);

  float _value;
  EEPROM.get(_add, _value);
  delay(50);
  return _value;
  
}
int HDC1080::write_value(float _value, int _add){
//  byte f_b, s_b, t_b, fo_b;
//    union {
//        float f;
//        byte b[4];
//    } u;
//    u.f = _value;
//    f_b = u.b[3];
//    s_b = u.b[2];
//    t_b = u.b[1];
//    fo_b = u.b[0];
//    EEPROM.write(_add, f_b);
//    delay(10);
//    EEPROM.write(_add + 1, s_b);
//    delay(10);
//    EEPROM.write(_add + 2, t_b);
//    delay(10);
//    EEPROM.write(_add + 3, fo_b);
//    delay(10);
//    //EEPROM.commit();
//    return;

    EEPROM.put(_add, _value);
    delay(50);
//    EEPROM.commit();
    return;
    
}
float HDC1080::byte_to_float(byte first_byte, byte second_byte, byte third_byte, byte fourth_byte) {
  union {
    float f;
    byte b[4];
  } u;
  u.b[3] = fourth_byte;
  u.b[2] = third_byte;
  u.b[1] = second_byte;
  u.b[0] = first_byte;
  return u.f;
}
