#define DEBUG  ///Debug
#include <EEPROM.h>
#include <Arduino.h>
#include "HDC1080.h"
#include "SPS30_sensor.h"
#include "SoftwareSerial.h"
#define RS485PinControl 8   //RS485 Direction control0 OA320
//#define RS485PinControl 9   //RS485 Direction control0

#define RS485Transmit    HIGH
#define RS485Receive     LOW
#define TIME_HANDLE 20
#define TIME_SENSOR 5000
#define MAX_BUFFER_SIZE_HAL 50
int RH;

SoftwareSerial mySerial(10, 11);
//SoftwareSerial mySerial(12, 11);

HDC1080 HDC1080;
Dust Dust;
//N1101
byte deviceId = 0x01;
String ver = "Verision.01.22";
String info_name = "NS8NQ";
String strcommand[6];
 

void setup(){
	mySerial.begin(9600);
	pinMode(RS485PinControl, OUTPUT);
	digitalWrite(RS485PinControl, LOW);
	deviceId = EEPROM.read(0x00);
    if (deviceId == 0xFF) {
        deviceId = 0x01;
        EEPROM.write(0x00, deviceId);
    }

      HDC1080.setup();
      
      Dust.setup();
//  if(read_value(50) > 0){
//    RH = read_value(50);
//  }else{
//    RH = 75;
//    write_value(75, 50);
//    delay(10);
//  }
  send_to_485("done");
}

void loop(){
    wait_for_serial();
//     serialEvent();
}
int wait_for_serial(){
    int i = 0;
    int ret;
    for (i = 0; i < TIME_HANDLE; i++) {
        delay(50);
        ret = serialEvent();
        if (ret >= 0)
            return ret;
    }
    return -1;
}
void send_to_485(String msg){
	  digitalWrite(RS485PinControl, HIGH);
    delay(250);
    mySerial.println(msg);
    delay(250);
    digitalWrite(RS485PinControl, LOW);
    delay(50);
    
}
void send_error_to_485(){
	String msg = "?";
    char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
    send_to_485(msg); 
}
int serialEvent() {
    byte resquest[MAX_BUFFER_SIZE_HAL];
    memset(resquest, 0, MAX_BUFFER_SIZE_HAL);
    byte buffSize = 0;
    while (mySerial.available()>0) {
  
          byte recv = (byte)mySerial.read();
          //Serial.print((char)recv);
           if (recv == '\n' || recv == '\r') {
              break;
          }
          resquest[buffSize] = recv;
          buffSize++;
          if (buffSize >= MAX_BUFFER_SIZE_HAL) {
              break;
          }
          delay(5);
      }
//    mySerial.end();
//    mySerial.begin(9600);
    if (buffSize > 3) {
        
        read_cmd(resquest, buffSize); 
        int ret = serial_handle(resquest, buffSize);
        return ret;
    }
    return -1;
}

int serial_handle(byte buf[], unsigned char bufferSize) {
    byte keyControl = buf[0];

    if (keyControl == '?') {    //Read
        if ((buf[1] == 'i') && (buf[2] == 'd')) {
            handle_function_readID();
            return 1;
        }
        else {
            send_error_to_485();
            return -1;
        }
    }
    byte id = hex2b(buf[1], buf[2]);
    byte keyCR = buf[bufferSize - 1];


    //Khác ID báo lỗi
    if ((id != deviceId) || (keyCR != '*')) {
        //send_error_to_485();
        return -1;
    }
    char keyFunction = buf[3];

    if (keyControl == '%') {   //write
        handle_function_read(keyFunction, buf[4]);
         return 1;
    }
    else if (keyControl == '$') {   //config  
         handle_function_config(buf, bufferSize);
         return 1;
    }
    else if(keyControl == '#'){
        handle_function_read_sensor(buf, bufferSize);
        return 1;
    }
    else {
        send_error_to_485();
        return -1;
    }
}

void handle_function_readID(){
	  String msg = "!";
    char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
    send_to_485(msg); 
}

void handle_function_read(char func, char func_read){
	if(func == '1') handle_function_readID();
	else if(func ==  'C'){
	    handle_function_read_calib(func_read);
	}
	else if(func == 'F'){
		handle_function_read_firmware();
	}else if(func == 'N'){
	  handle_function_read_name();
	}else if(func == 'A'){
	  handle_function_read_info();
	}
  else if(func == 'R'){
    handle_function_read_RH();
  }
	else send_error_to_485();
}

void handle_function_config(byte buf[],unsigned char buffSize){
	if(buf[3] == 'I') handle_function_config_ID(buf, buffSize);
	else if(buf[3] == 'C') handle_function_config_calib(buf, buffSize);
  else if(buf[3] == 'R') handle_function_config_RH(buf, buffSize);
  else send_error_to_485();
}
void handle_function_read_sensor(byte buf[], unsigned char buffSize){
    if(buf[3] == 'D'){
//        RH = read_value(50);
        HDC1080.read();
        delay(5);
//        if( HDC1080.value.humi > RH){
//          Dust.read(1);
//        }else Dust.read(0);
        Dust.read(0);
        

        String msg = "!";
        char hex_string[3];
        sprintf(hex_string, "%02X", deviceId);
        msg += String(hex_string);
        msg+= ",Temp,";
        msg+=String(HDC1080.value.temp);
        
        msg+=",Humi,";
        msg+=String(HDC1080.value.humi);

        msg+=",Pm25,";
        msg+=String(Dust.value_dust.pm25);

        msg+=",Pm10,";
        msg+=String(Dust.value_dust.pm10);

        msg+="*";
        send_to_485(msg);
    }
    else send_error_to_485();

}
void handle_function_read_calib(char mode){
	String msg = "!";
	char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
	if(mode == 'A'){
		msg+=",";
		msg+="calib_temp,";
    msg+=readStringFromEEPROM(4);
	 	msg+=",";

	 	msg+=String(HDC1080.oTemp);
	 	msg+="*";
	 	send_to_485(msg);
	 	return;
	}else if(mode == 'B'){
		msg+=",";
		msg+="calib_humi,";
    msg+=readStringFromEEPROM(14);
	 	msg+=",";

	 	msg+=String(HDC1080.oHum);
	 	msg+="*";
	 	send_to_485(msg);
	 	return;
	}else if(mode == 'C'){
	 	msg+=",";
		msg+="calib_pm25,";
    msg+=readStringFromEEPROM(24);

	 	msg+=",";
	 	msg+=String(Dust.aPm25);
	 	msg+=",";
	 	msg+=String(Dust.bPm25);
	 	msg+="*";
	 	send_to_485(msg);
	 	return;
	    
	}else if(mode == 'D'){
		msg+=",";
		msg+="calib_pmm10,";
    msg+=readStringFromEEPROM(34);
	 	msg+=",";
	 	msg+=String(Dust.aPm10);
	 	msg+=",";
	 	msg+=String(Dust.bPm10);
	 	msg+="*";
	 	send_to_485(msg);
	 	return;
	}
	else send_error_to_485();
}
void handle_function_read_RH(){
  String msg = "!";
  char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
    msg+=",";
    msg+="RH,";
    RH = read_value(50);
    msg+=String(RH);
    msg+="*";
    send_to_485(msg);
    return;
}

void handle_function_read_firmware(){
	String msg = "!";
	char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
    msg+=ver;
    msg+="*";
    send_to_485(msg);

}
void handle_function_read_name(){
	String msg = "!";
	char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
    msg+=info_name;
    msg+="*";
    send_to_485(msg);
}
void handle_function_read_info(){
	String msg = "!";
	char hex_string[3];
    sprintf(hex_string, "%02X", deviceId);
    msg += String(hex_string);
    msg +=",";
    msg +=ver;
    msg +=",";
	  msg+=readStringFromEEPROM(4);
    msg +=",";
    msg+=readStringFromEEPROM(14);
    msg +=",";
    msg+=readStringFromEEPROM(24);
    msg +=",";
    msg+=readStringFromEEPROM(34);
	  msg+="*";
    send_to_485(msg);
}
void handle_function_config_ID(byte buf[],unsigned char buffSize){
    byte new_id = hex2b(buf[4], buf[5]);
    if(new_id == 0x00) send_error_to_485();
    else {
        
        EEPROM.write(0x00, new_id);
        delay(10);
        String msg = "!";
        char hex_string[3];
        sprintf(hex_string, "%02X", deviceId);
        msg += String(hex_string);
        sprintf(hex_string, "%02X", new_id);
        msg += String(hex_string);
        msg +="*";
        send_to_485(msg);
        deviceId = new_id;
    }
}
void handle_function_config_RH(byte buf[],unsigned char buffSize){
  float RH_new = atof(strcommand[1].c_str());
  bool _b = true;
  _b = _b && (strcommand[1] != "");
  if (_b == false) {
      //Serial.println("Ban tin khong hop le");
      send_error_to_485();
      return;
  }
  write_value(RH_new, 50);
  handle_function_read_RH();
  
}
void handle_function_config_calib(byte buf[],unsigned char buffSize){
    unsigned long date  = atoi(strcommand[1].c_str());
    bool _b = true;
    
    if(buf[4] == 'A'){
        float oTemp_new = atof(strcommand[2].c_str());
        _b = _b && (strcommand[2] != "");
        if (_b == false) {
            //Serial.println("Ban tin khong hop le");
            send_error_to_485();
            return;
        }
        if(isnan(oTemp_new)) return;
        if(!check_date(strcommand[1])) return;
        write_value(oTemp_new, 120);
        writeStringToEEPROM(4,strcommand[1]);
        HDC1080.read_calib();
        handle_function_read_calib('A');            
        
    }
    else if(buf[4] == 'B'){
         float oHum_new = atof(strcommand[2].c_str());
          _b = _b && (strcommand[2] != "");
         if (_b == false) {
            //Serial.println("Ban tin khong hop le");
            send_error_to_485();
            return;
          }
          if(isnan(oHum_new)) return;
          if(!check_date(strcommand[1])) return;
          write_value(oHum_new, 125);
          writeStringToEEPROM(14,strcommand[1]);
          HDC1080.read_calib();
          handle_function_read_calib('B');            
    }
    else if(buf[4] == 'C'){
        float aPm25_new = atof(strcommand[2].c_str());
        float bPm25_new = atof(strcommand[3].c_str());
        _b = (strcommand[2] != "") && (strcommand[3] != "");
        if (_b == false) {
            //Serial.println("Ban tin khong hop le");
            send_error_to_485();
            return;
        }
        if(isnan(aPm25_new) || isnan(bPm25_new)) return;
        if(!check_date(strcommand[1])) return;
        write_value(aPm25_new, 100);
        write_value(bPm25_new, 105);
        writeStringToEEPROM(24,strcommand[1]);
        Dust.read_calib();
        handle_function_read_calib('C');
   
     }else if(buf[4] == 'D'){
        float aPm10_new = atof(strcommand[2].c_str());
        float bPm10_new = atof(strcommand[3].c_str());
        
        _b = (strcommand[2] != "") && (strcommand[3] != "");
        if (_b == false) {
            //Serial.println("Ban tin khong hop le");
            send_error_to_485();
            return;
        }
        if(isnan(aPm10_new) || isnan(bPm10_new)) return;
        if(!check_date(strcommand[1])) return;
        write_value(aPm10_new, 110);
        write_value(bPm10_new, 115);
        writeStringToEEPROM(34,strcommand[1]);
        Dust.read_calib();
        handle_function_read_calib('D');
    }else {
      send_error_to_485();
      return;
    }
}

void read_cmd(byte buf[],unsigned char buffSize){
  char c; 
  int j = 0;
  for(int i = 0; i< 6; i++){
     strcommand[i] = "";
  }

  for (int i = 0; i < buffSize; i++) {
    c = (char)buf[i];
    if(c == '*') break;
    if (c == ','){
      j++;
      continue;
    }
    strcommand[j] += c;//&& isDigit(c)
  }
    for(int i = 0; i< 6; i++){
  }
}

char nibble2c(char c)
{
    if ((c >= '0') && (c <= '9'))
        return c - '0';
    if ((c >= 'A') && (c <= 'F'))
        return c + 10 - 'A';
    if ((c >= 'a') && (c <= 'f'))
        return c + 10 - 'a';
    return -1;
}


byte hex2b(char c1, char c2)
{
    if (nibble2c(c2) >= 0)
        return nibble2c(c1) * 16 + nibble2c(c2);
    return (byte)nibble2c(c1);
}
float read_value(int _add) {
  float _value;
  EEPROM.get(_add, _value);
  delay(50);
  return _value;
//  byte f_b, s_b, t_b, fo_b;
//  f_b = EEPROM.read(_add);
//  delay(10);
//  if (f_b == 0xFF) {
//    f_b = 0;
//  }
//  s_b = EEPROM.read(_add+1);
//  delay(10);
//  if (s_b == 0xFF) {
//    s_b = 0;
//  }
//  t_b = EEPROM.read(_add+2);
//  delay(10);
//  if (t_b == 0xFF) {
//    t_b = 0;
//  }
//  fo_b = EEPROM.read(_add+3);
//  delay(10);
//  if (fo_b == 0xFF) {
//    fo_b = 0;
//  }
//  return byte_to_float(fo_b, t_b, s_b, f_b);
}
int write_value(float _value, int _add){
//  byte f_b, s_b, t_b, fo_b;
//    union {
//        float f;
//        byte b[4];
//    } u;
//    u.f = _value;
//    f_b = u.b[3];
//    s_b = u.b[2];
//    t_b = u.b[1];
//    fo_b = u.b[0];
//    EEPROM.write(_add, f_b);
//    delay(10);
//    EEPROM.write(_add + 1, s_b);
//    delay(10);
//    EEPROM.write(_add + 2, t_b);
//    delay(10);
//    EEPROM.write(_add + 3, fo_b);
//    delay(10);

    EEPROM.put(_add, _value);
    delay(50);
//    EEPROM.commit();
    return;
}

void writeStringToEEPROM(int addrOffset, const String &strToWrite)
{
  byte len = strToWrite.length();
  EEPROM.write(addrOffset, len);
  for (int i = 0; i < len; i++)
  {
    EEPROM.write(addrOffset + 1 + i, strToWrite[i]);
  }
}

String readStringFromEEPROM(int addrOffset)
{
  int newStrLen = EEPROM.read(addrOffset);
  char data[newStrLen + 1];
  for (int i = 0; i < newStrLen; i++)
  {
    data[i] = EEPROM.read(addrOffset + 1 + i);
  }
  data[newStrLen] = '\0'; // the character may appear in a weird way, you should read: 'only one backslash and 0'
  return String(data);
}
float byte_to_float(byte first_byte, byte second_byte, byte third_byte, byte fourth_byte) {
  union {
    float f;
    byte b[4];
  } u;
  u.b[3] = fourth_byte;
  u.b[2] = third_byte;
  u.b[1] = second_byte;
  u.b[0] = first_byte;
  return u.f;
}
/// time tick

bool check_date(String _date){
    String tg;
    if(_date.length() != 6) return false;
    int t;
    tg = _date.substring(0,2);
    t = tg.toInt();
    if(t < 0) return false;
    tg = _date.substring(2,4);
    t = tg.toInt();
    if(t < 0 || t > 12) return false;
    tg = _date.substring(4,6);
    t = tg.toInt();
    if(t < 0 || t > 31) return false;
    return true;
}

/*
dia chi 0: dia chi thiet bij
dia chi 4: ngay calib nhiet do
dia chi 14: ngay calib do am
dia chi 24: ngay calib nong do PM25
dia chi 34: ngay calib nong do PM10
dia chi 50: gia tri RH. $01R,75*
dia chi 120: giá trị calib nhiệt độ
dia chỉ 125: giá trị calib độ ậm
dia chỉ 100: giá trị a của pm25
dia chỉ 105: giá trị b của pm25
dịa chỉ 110: giá trị a của pm10
địa chỉ 115: giá trị b của pm10
*/
