#include <CircularBuffer.h>
#include <EEPROM.h>
#include <Wire.h>
#include "sps30.h"

#define _use_sps30 1
#if _use_sps30
  #define SPS30_COMMS SERIALPORT
#endif

#define MAX_BUFFER_SIZE 8

#if defined(ARDUINO) && ARDUINO >= 100
	#include "arduino.h"
#else
	#include "WProgram.h"
#endif


#define Dust_Serial Serial
#define Dust_baudrate 115200
#define TX1 4
#define RX1 2
 //(seconds)



class Dust{
public:
struct Dust_value
{
    float pm25 = -1;
    float pm10= -1;
    int status = 0;
}value_dust;
CircularBuffer<double, 20> count_err_dust;
	float aPm25, bPm25, aPm10, bPm10;
	Dust();
	~Dust();
	void setup();
  void read_calib();
	int read(int n);
	void calibrate(float x1, float y1, float x2, float y2, int add1, int add2);
 	float readPm25();
 	float readPm10();
 	void calibrate();
  int check_status_sensor();
	float read_value(int _add);
  int write_value(float _value, int _add);
 	float byte_to_float(byte first_byte, byte second_byte, byte third_byte, byte fourth_byte);

};
