#include "SPS30_sensor.h"
SPS30 sps30;
Dust::Dust() {
}

Dust::~Dust() {
}

void Dust::setup() {
  sps30.begin(SPS30_COMMS,115200);
  delay(1000);
  int count_r = 0;
  if(!sps30.reset()){
    return;
  }
  if(!sps30.start()){
    return;
  };
    
	read_calib();
  
}

void Dust::read_calib(){
  aPm25 = read_value(100);
  bPm25 = read_value(105);
  aPm10 = read_value(110);
  bPm10 = read_value(115);
  if(isnan(aPm25) || aPm25 == 0.00) {
    aPm25 = 1.0;
    write_value(aPm25, 100);
  }
  if(isnan(bPm25)) {
    bPm25 = 0.0;
    write_value(bPm25, 105);
  }
  if(isnan(aPm10) || aPm10 == 0.00) {
    aPm10 = 1.0;
    write_value(aPm10, 110);
  }
  if(isnan(bPm10)) {
    bPm10 = 0.0;
    write_value(bPm10, 115);
  }
}

int Dust::read(int n) {
    uint8_t ret, error_cnt = 0;
    struct sps_values val;
    float p25;
	  float p10;
//    read_calib();
    ret = sps30.Getvalue(&val);
    if(ret != ERR_OK){
      value_dust.pm25 = -1;
      value_dust.pm10 = -1;  
      return -1;
    }
    value_dust.pm25 = val.MassPM2;
    value_dust.pm10 = val.MassPM10;  
    
    if ((aPm25 == 0) && (bPm25 == 0)) {
      aPm25 = 1;
      bPm25 = 0;
    }
    if(value_dust.pm25 < 1) {
        value_dust.pm25 = -1;
    }else if(value_dust.pm25 > 600){
        value_dust.pm25 = -1;
    }else{
        if((value_dust.pm25*aPm25 + bPm25) < 1) {
          value_dust.pm25 = 1;
        }else if((value_dust.pm25*aPm25 + bPm25) > 600){
          value_dust.pm25 = 600;
        }else 
            value_dust.pm25 = value_dust.pm25*aPm25 + bPm25;
    }
  
    if ((aPm10 == 0) && (bPm10 == 0)) {
      aPm10 = 1;
      bPm10 = 0;
    }

    if(value_dust.pm10 < 1) {
        value_dust.pm10 = -1;
    }else if(value_dust.pm10 > 600){
        value_dust.pm10 = -1;
    }else{
        if((value_dust.pm10*aPm10 + bPm10) < 1) {
          value_dust.pm10 = 1;
        }else if((value_dust.pm10*aPm10 + bPm10) > 600){
          value_dust.pm10 = 600;
        }else 
            value_dust.pm10 = value_dust.pm10*aPm10 + bPm10;
    }
    
//    if(n == 1){
//      value_dust.pm25 = value_dust.pm25 * 0.5893 + 14.541;
//    }else {
//      value_dust.pm25 = value_dust.pm25;
//    }
    return 1;

}

void Dust::calibrate(float x1, float y1, float x2, float y2, int add1, int add2) {
  float a,b;
    if ((y2 - y1) == 0) {
      return;
    }
    else {
        a = (y2 - y1) / (x2 - x1);
        b = y1 - a * x1;
    }
    write_value(a, add1);
    write_value(b, add2);

}
 
int Dust::check_status_sensor(){
  int status_dust = 0;
    for (int i = 0; i < 20; i++) {
        if (count_err_dust[i] != 1){
          status_dust = 1;
          break;
      }
    }
    return status_dust;
}
float Dust::read_value(int _add) {
//	 byte f_b, s_b, t_b, fo_b;
//  f_b = EEPROM.read(_add);
//  delay(10);
//  if (f_b == 0xFF) {
//    f_b = 0;
//  }
//  s_b = EEPROM.read(_add+1);
//  delay(10);
//  if (s_b == 0xFF) {
//    s_b = 0;
//  }
//  t_b = EEPROM.read(_add+2);
//  delay(10);
//  if (t_b == 0xFF) {
//    t_b = 0;
//  }
//  fo_b = EEPROM.read(_add+3);
//  delay(10);
//  if (fo_b == 0xFF) {
//    fo_b = 0;
//  }
//  return byte_to_float(fo_b, t_b, s_b, f_b);
  float _value;
  EEPROM.get(_add, _value);
  delay(50);
  return _value;
}
int Dust::write_value(float _value, int _add){
//    byte f_b, s_b, t_b, fo_b;
//    union {
//        float f;
//        byte b[4];
//    } u;
//    u.f = _value;
//    f_b = u.b[3];
//    s_b = u.b[2];
//    t_b = u.b[1];
//    fo_b = u.b[0];
//    EEPROM.write(_add, f_b);
//    delay(10);
//    EEPROM.write(_add + 1, s_b);
//    delay(10);
//    EEPROM.write(_add + 2, t_b);
//    delay(10);
//    EEPROM.write(_add + 3, fo_b);
//    delay(10);
//    //EEPROM.commit();
//    return;
    EEPROM.put(_add, _value);
    delay(50);
//    EEPROM.commit();
    return;
}
float Dust::byte_to_float(byte first_byte, byte second_byte, byte third_byte, byte fourth_byte) {
  union {
    float f;
    byte b[4];
  } u;
  u.b[3] = fourth_byte;
  u.b[2] = third_byte;
  u.b[1] = second_byte;
  u.b[0] = first_byte;
  return u.f;
}
