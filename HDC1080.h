#include <Wire.h>
#include "ClosedCube_HDC1080.h"
#include <CircularBuffer.h>
#include <EEPROM.h>
#if defined(ARDUINO) && ARDUINO >= 100
	#include "arduino.h"
#else
	#include "WProgram.h"
#endif

#define SHT_dataPin  21
#define SHT_clockPin 22


class HDC1080 {
public:
  CircularBuffer<double, 20> count_err_hdc;
	struct HDC_value
	{
	    float temp = -1;
	    float humi= -1;
	    int status = 1;
	}value;
	float oTemp, oHum;
	HDC1080();
    ~HDC1080();
	void setup();
  void read_calib();
	void read();
	void calibrate(float a,float b);
  int check_status_sensor();
  float readTemp();
  float readHumi();
  float read_value(int _add);
	int write_value(float _value, int _add);
	float byte_to_float(byte first_byte, byte second_byte, byte third_byte, byte fourth_byte);
 };
